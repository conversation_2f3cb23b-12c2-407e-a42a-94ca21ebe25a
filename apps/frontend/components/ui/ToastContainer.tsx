/**
 * Toast容器组件
 * 🎯 核心价值：全局Toast消息容器，管理多个Toast的显示
 * 📦 功能范围：Toast布局管理、动画协调、全局渲染
 * 🔄 架构设计：基于Zustand状态的全局容器
 */

'use client';

import React from 'react';
import { useToastStore } from '@/core/ui/ToastStore';
import Toast from './Toast';

// ===== 类型定义 =====

export interface ToastContainerProps {
  /** 容器位置 */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  /** 最大显示数量 */
  maxToasts?: number;
  /** 自定义类名 */
  className?: string;
}

// ===== 位置样式配置 =====

const positionStyles = {
  'top-right': 'top-4 right-4',
  'top-left': 'top-4 left-4',
  'bottom-right': 'bottom-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
  'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
};

// ===== ToastContainer组件 =====

const ToastContainer: React.FC<ToastContainerProps> = ({
  position = 'top-right',
  maxToasts = 5,
  className = ''
}) => {
  const { toasts, removeToast } = useToastStore();

  // 限制显示数量，显示最新的toast
  const visibleToasts = toasts.slice(-maxToasts);

  if (visibleToasts.length === 0) {
    return null;
  }

  return (
    <div
      className={`
        fixed z-50 pointer-events-none
        ${positionStyles[position]}
        ${className}
      `}
      aria-live="polite"
      aria-label="通知消息"
    >
      <div className="flex flex-col space-y-2 pointer-events-auto">
        {visibleToasts.map((toast, index) => (
          <Toast
            key={toast.id}
            toast={toast}
            onClose={removeToast}
            index={index}
          />
        ))}
      </div>
    </div>
  );
};

export default ToastContainer;
